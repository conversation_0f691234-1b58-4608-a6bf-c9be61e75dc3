package diet

import (
	"context"

	"shikeyinxiang-goframe/api/diet/v1"
)

// IDietV1 饮食记录管理接口定义
type IDietV1 interface {
	// DietRecordAdd 添加饮食记录
	DietRecordAdd(ctx context.Context, req *v1.DietRecordAddReq) (res *v1.DietRecordAddRes, err error)
	
	// DietRecordList 获取饮食记录列表
	DietRecordList(ctx context.Context, req *v1.DietRecordListReq) (res *v1.DietRecordListRes, err error)
	
	// DietRecordDetail 获取饮食记录详情
	DietRecordDetail(ctx context.Context, req *v1.DietRecordDetailReq) (res *v1.DietRecordDetailRes, err error)
	
	// DietRecordUpdate 更新饮食记录
	DietRecordUpdate(ctx context.Context, req *v1.DietRecordUpdateReq) (res *v1.DietRecordUpdateRes, err error)
	
	// DietRecordDelete 删除饮食记录
	DietRecordDelete(ctx context.Context, req *v1.DietRecordDeleteReq) (res *v1.DietRecordDeleteRes, err error)
	
	// DietRecordStats 获取饮食记录统计
	DietRecordStats(ctx context.Context, req *v1.DietRecordStatsReq) (res *v1.DietRecordStatsRes, err error)
	
	// PopularFoods 获取热门食物统计
	PopularFoods(ctx context.Context, req *v1.PopularFoodsReq) (res *v1.PopularFoodsRes, err error)
	
	// ActiveUsers 获取活跃用户统计（管理员功能）
	ActiveUsers(ctx context.Context, req *v1.ActiveUsersReq) (res *v1.ActiveUsersRes, err error)
}
