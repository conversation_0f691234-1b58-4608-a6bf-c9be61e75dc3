package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
)

// IFoodV1 食物数据管理接口定义
type IFoodV1 interface {
	// FoodList 获取食物列表
	FoodList(ctx context.Context, req *v1.FoodListReq) (res *v1.FoodListRes, err error)
	
	// FoodDetail 获取食物详情
	FoodDetail(ctx context.Context, req *v1.FoodDetailReq) (res *v1.FoodDetailRes, err error)
	
	// FoodCreate 创建食物（管理员功能）
	FoodCreate(ctx context.Context, req *v1.FoodCreateReq) (res *v1.FoodCreateRes, err error)
	
	// FoodUpdate 更新食物信息（管理员功能）
	FoodUpdate(ctx context.Context, req *v1.FoodUpdateReq) (res *v1.FoodUpdateRes, err error)
	
	// FoodDelete 删除食物（管理员功能）
	FoodDelete(ctx context.Context, req *v1.FoodDeleteReq) (res *v1.FoodDeleteRes, err error)
	
	// CategoryList 获取食物分类列表
	CategoryList(ctx context.Context, req *v1.CategoryListReq) (res *v1.CategoryListRes, err error)
	
	// CategoryCreate 创建食物分类（管理员功能）
	CategoryCreate(ctx context.Context, req *v1.CategoryCreateReq) (res *v1.CategoryCreateRes, err error)
	
	// CategoryUpdate 更新食物分类（管理员功能）
	CategoryUpdate(ctx context.Context, req *v1.CategoryUpdateReq) (res *v1.CategoryUpdateRes, err error)
	
	// CategoryDelete 删除食物分类（管理员功能）
	CategoryDelete(ctx context.Context, req *v1.CategoryDeleteReq) (res *v1.CategoryDeleteRes, err error)

	// FoodImageUploadUrl 获取食物图片上传URL（管理员功能）
	FoodImageUploadUrl(ctx context.Context, req *v1.FoodImageUploadUrlReq) (res *v1.FoodImageUploadUrlRes, err error)
}
