package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
)

// INutritionV1 营养分析管理接口定义
type INutritionV1 interface {
	// DailyNutrition 获取每日营养统计
	DailyNutrition(ctx context.Context, req *v1.DailyNutritionReq) (res *v1.DailyNutritionRes, err error)
	
	// NutritionTrend 获取营养趋势分析
	NutritionTrend(ctx context.Context, req *v1.NutritionTrendReq) (res *v1.NutritionTrendRes, err error)
	
	// NutritionDetails 获取营养摄入详情
	NutritionDetails(ctx context.Context, req *v1.NutritionDetailsReq) (res *v1.NutritionDetailsRes, err error)
	
	// NutritionAdvice 获取营养建议
	NutritionAdvice(ctx context.Context, req *v1.NutritionAdviceReq) (res *v1.NutritionAdviceRes, err error)
	
	// HealthReport 获取健康报告
	HealthReport(ctx context.Context, req *v1.HealthReportReq) (res *v1.HealthReportRes, err error)
	
	// ComplianceRate 获取营养达标率（管理员功能）
	ComplianceRate(ctx context.Context, req *v1.ComplianceRateReq) (res *v1.ComplianceRateRes, err error)
	
	// NutritionGoal 获取营养目标
	NutritionGoal(ctx context.Context, req *v1.NutritionGoalReq) (res *v1.NutritionGoalRes, err error)
	
	// NutritionGoalSet 设置营养目标
	NutritionGoalSet(ctx context.Context, req *v1.NutritionGoalSetReq) (res *v1.NutritionGoalSetRes, err error)
	
	// NutritionSummary 获取营养分析汇总（管理员功能）
	NutritionSummary(ctx context.Context, req *v1.NutritionSummaryReq) (res *v1.NutritionSummaryRes, err error)
}
