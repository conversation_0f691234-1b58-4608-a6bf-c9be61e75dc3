package auth

import (
	"context"

	"shikeyinxiang-goframe/api/auth/v1"
)

// IAuthV1 认证接口定义
type IAuthV1 interface {
	// UserLogin 用户登录
	UserLogin(ctx context.Context, req *v1.UserLoginReq) (res *v1.UserLoginRes, err error)
	
	// AdminLogin 管理员登录
	AdminLogin(ctx context.Context, req *v1.AdminLoginReq) (res *v1.AdminLoginRes, err error)
	
	// WechatLogin 微信登录
	WechatLogin(ctx context.Context, req *v1.WechatLoginReq) (res *v1.WechatLoginRes, err error)
	
	// Register 用户注册
	Register(ctx context.Context, req *v1.RegisterReq) (res *v1.RegisterRes, err error)
	
	// Logout 用户登出
	Logout(ctx context.Context, req *v1.LogoutReq) (res *v1.LogoutRes, err error)
	
	// CurrentUser 获取当前用户信息
	CurrentUser(ctx context.Context, req *v1.CurrentUserReq) (res *v1.CurrentUserRes, err error)
	
	// ChangePassword 修改密码
	ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (res *v1.ChangePasswordRes, err error)
}
